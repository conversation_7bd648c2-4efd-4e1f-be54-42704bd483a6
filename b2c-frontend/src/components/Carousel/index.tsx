import type React from 'react';
import { useEffect, useState } from 'react';
import { Dimensions, Pressable, View, type ViewStyle, Text } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedReaction,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { twMerge } from 'tailwind-merge';
import ChevronLeft from '@/src/assets/svgs/ChevronLeft';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import type { CarouselProps } from './types';

const { width } = Dimensions.get('window');

const Carousel = ({
  children,
  showArrows = true,
  showDots = true,
  showSlideNumbers = false,
  autoPlay = false,
  duration = 5000,
  className,
  dotClassName,
  arrowClassName,
  activeColor = '#1B4332',
  inactiveColor = '#D1FAE5',
  initialIndex = 0,
}: CarouselProps): React.JSX.Element => {
  const safeInitialIndex = Math.max(0, Math.min(initialIndex, children.length - 1));
  const [currentIndex, setCurrentIndex] = useState<number>(safeInitialIndex);
  const translateX = useSharedValue<number>(-safeInitialIndex * width);
  const isPanning = useSharedValue<boolean>(false);
  const startX = useSharedValue<number>(0);

  const activeIndex = useDerivedValue(() => {
    const index = Math.round(Math.abs(translateX.value) / width);
    return Math.max(0, Math.min(index, children.length - 1));
  });

  useAnimatedReaction(
    () => activeIndex.value,
    (value) => {
      runOnJS(setCurrentIndex)(value);
    },
  );

  const goToSlide = (index: number): void => {
    const clampedIndex = Math.max(0, Math.min(index, children.length - 1));
    translateX.value = withTiming(-clampedIndex * width, {
      duration: 400,
    });
  };

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;

    if (autoPlay && !isPanning.value) {
      interval = setInterval(() => {
        const nextIndex = (currentIndex + 1) % children.length;
        goToSlide(nextIndex);
      }, duration);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoPlay, duration, children.length, currentIndex, goToSlide, isPanning.value]);

  const panGesture = Gesture.Pan()
    .onBegin(() => {
      console.log('')
      isPanning.value = true;
      startX.value = translateX.value;
    })
    .onUpdate((event) => {
      console.log('update')
      // Only allow pan updates if arrows are shown (not zoomed)
      if (!showArrows) return;

      const newTranslateX = startX.value + event.translationX;
      const minTranslateX = -(children.length - 1) * width;
      const maxTranslateX = 0;

      if (newTranslateX > maxTranslateX) {
        const overscroll = newTranslateX - maxTranslateX;
        const resistance = Math.max(0.1, 1 - overscroll / (width * 0.5));
        translateX.value = maxTranslateX + overscroll * resistance;
      } else if (newTranslateX < minTranslateX) {
        const overscroll = minTranslateX - newTranslateX;
        const resistance = Math.max(0.1, 1 - overscroll / (width * 0.5));
        translateX.value = minTranslateX - overscroll * resistance;
      } else {
        translateX.value = newTranslateX;
      }
    })
    .onEnd((event) => {
      isPanning.value = false;
      console.log('end')
      // Only allow navigation if arrows are shown (not zoomed)
      if (!showArrows) return;

      const currentPos = Math.abs(translateX.value);
      const currentIdx = Math.round(currentPos / width);
      const threshold = width * 0.3;
      const velocity = Math.abs(event.velocityX);

      let targetIndex = currentIdx;

      if (Math.abs(event.translationX) > threshold || velocity > 800) {
        if (event.translationX > 0) {
          targetIndex = Math.max(0, currentIdx - 1);
        } else {
          targetIndex = Math.min(children.length - 1, currentIdx + 1);
        }
      }

      // Use goToSlide function for consistent navigation
      runOnJS(goToSlide)(targetIndex);
    })
    .minDistance(5)
    .activeOffsetX([-8, 8])
    .failOffsetY([-30, 30]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const dotStyle = (index: number): ViewStyle => ({
    width: 25,
    height: 8,
    borderRadius: 24,
    backgroundColor: currentIndex === index ? activeColor : inactiveColor,
  });

  if (!children || children.length === 0) {
    return <View className={twMerge('relative w-full overflow-hidden', className)} />;
  }

  return (
    <View className={twMerge('relative w-full overflow-hidden', className)}>
      <GestureDetector gesture={panGesture}>
        <Animated.View className="flex-row" style={animatedStyle}>
          {children.map((child, index) => (
            <View key={index} style={{ width }}>
              {child}
            </View>
          ))}
        </Animated.View>
      </GestureDetector>

      {showArrows && (
        <>
          <Pressable
            onPress={() => goToSlide(currentIndex - 1)}
            disabled={currentIndex === 0}
            className={twMerge(
              'absolute left-4 top-1/2 -translate-y-1/2 rounded-full bg-white/80 p-2 shadow-md',
              currentIndex === 0 && 'opacity-50',
              arrowClassName,
            )}
          >
            <ChevronLeft />
          </Pressable>
          <Pressable
            onPress={() => goToSlide(currentIndex + 1)}
            disabled={currentIndex === children.length - 1}
            className={twMerge(
              'absolute right-4 top-1/2 -translate-y-1/2 rounded-full bg-white/80 p-2 shadow-md',
              currentIndex === children.length - 1 && 'opacity-50',
              arrowClassName,
            )}
          >
            <ChevronRight />
          </Pressable>
        </>
      )}

      {showSlideNumbers && (
        <View className="absolute bottom-4 right-4 bg-black/50 px-3 py-1 rounded-full">
          <Text className="text-white font-medium">
            {currentIndex + 1}/{children.length}
          </Text>
        </View>
      )}

      {showDots && (
        <View className="flex-row gap-4 mt-14 mx-auto">
          {children.map((_, index) => (
            <Pressable
              key={index}
              onPress={() => goToSlide(index)}
              style={dotStyle(index)}
              className={twMerge('transition-all duration-300', dotClassName)}
            />
          ))}
        </View>
      )}
    </View>
  );
};

export default Carousel;
